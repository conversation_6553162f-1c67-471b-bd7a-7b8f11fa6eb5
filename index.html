<!DOCTYPE html>
<html lang="ka">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevDoses - შესვლა</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: #1e293b;
        }

        .auth-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            width: 100%;
            max-width: 420px;
            padding: 48px 32px;
            position: relative;
            border: 1px solid #e2e8f0;
        }

        .logo {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo h1 {
            color: #0f172a;
            font-size: 2.25rem;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.025em;
        }

        .logo p {
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 400;
        }

        .form-tabs {
            display: flex;
            margin-bottom: 32px;
            background: #f1f5f9;
            border-radius: 12px;
            padding: 4px;
            border: 1px solid #e2e8f0;
        }

        .tab-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            color: #64748b;
        }

        .tab-btn.active {
            background: white;
            color: #0f172a;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .form-container {
            position: relative;
        }

        .form {
            display: none;
        }

        .form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .input-wrapper {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 14px 16px 14px 44px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
            color: #111827;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group input::placeholder {
            color: #9ca3af;
        }

        .input-icon {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 1rem;
        }

        .password-toggle {
            position: absolute;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            cursor: pointer;
            font-size: 1rem;
            transition: color 0.2s ease;
        }

        .password-toggle:hover {
            color: #374151;
        }

        .forgot-password {
            text-align: right;
            margin-bottom: 28px;
        }

        .forgot-password a {
            color: #3b82f6;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .forgot-password a:hover {
            color: #1d4ed8;
        }

        .submit-btn {
            width: 100%;
            padding: 14px 16px;
            background: #0f172a;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .submit-btn:hover {
            background: #1e293b;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(15, 23, 42, 0.15);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        .divider {
            text-align: center;
            margin: 32px 0;
            position: relative;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
            z-index: 1;
        }

        .divider span {
            background: white;
            padding: 0 16px;
            position: relative;
            z-index: 2;
        }

        .social-login {
            display: flex;
            gap: 12px;
            margin-bottom: 0;
        }

        .social-btn {
            flex: 1;
            padding: 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1rem;
        }

        .social-btn:hover {
            border-color: #9ca3af;
            background: #f9fafb;
            transform: translateY(-1px);
        }

        .google { color: #ea4335; }
        .facebook { color: #1877f2; }
        .github { color: #24292f; }

        .error-message {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 0.875rem;
            display: none;
        }

        .success-message {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 0.875rem;
            display: none;
        }

        @media (max-width: 480px) {
            body {
                padding: 16px;
            }

            .auth-container {
                padding: 32px 24px;
                max-width: 100%;
            }

            .logo h1 {
                font-size: 2rem;
            }

            .form-group input {
                padding: 12px 14px 12px 40px;
            }

            .input-icon {
                left: 12px;
            }

            .password-toggle {
                right: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <h1>DevDoses</h1>
            <p>Daily doses of development wisdom</p>
        </div>

        <div class="form-tabs">
            <button class="tab-btn active" onclick="switchTab('login')">Login</button>
            <button class="tab-btn" onclick="switchTab('register')">Register</button>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <div class="form-container">
            <!-- Login Form -->
            <form class="form active" id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">Email</label>
                    <div class="input-wrapper">
                        <i class="fas fa-envelope input-icon"></i>
                        <input type="email" id="loginEmail" name="email" required placeholder="Your email">
                    </div>
                </div>

                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <div class="input-wrapper">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="loginPassword" name="password" required placeholder="Your password">
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('loginPassword')"></i>
                    </div>
                </div>

                <div class="forgot-password">
                    <a href="#" onclick="showForgotPassword()">Forgot password?</a>
                </div>

                <button type="submit" class="submit-btn">Login</button>
            </form>

            <!-- Register Form -->
            <form class="form" id="registerForm">
                <div class="form-group">
                    <label for="registerName">Name and surname</label>
                    <div class="input-wrapper">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" id="registerName" name="name" required placeholder="Your name and surname">
                    </div>
                </div>

                <div class="form-group">
                    <label for="registerEmail">Email</label>
                    <div class="input-wrapper">
                        <i class="fas fa-envelope input-icon"></i>
                        <input type="email" id="registerEmail" name="email" required placeholder="Your email">
                    </div>
                </div>

                <div class="form-group">
                    <label for="registerPassword">Password</label>
                    <div class="input-wrapper">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="registerPassword" name="password" required placeholder="Create a password">
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('registerPassword')"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">Confirm password</label>
                    <div class="input-wrapper">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="Confirm your password">
                        <i class="fas fa-eye password-toggle" onclick="togglePassword('confirmPassword')"></i>
                    </div>
                </div>

                <button type="submit" class="submit-btn">Register</button>
            </form>
        </div>

        <div class="divider">
            <span>or</span>
        </div>

        <div class="social-login">
            <button class="social-btn google" onclick="socialLogin('google')">
                <i class="fab fa-google"></i>
            </button>
            <button class="social-btn facebook" onclick="socialLogin('facebook')">
                <i class="fab fa-facebook-f"></i>
            </button>
            <button class="social-btn github" onclick="socialLogin('github')">
                <i class="fab fa-github"></i>
            </button>
        </div>
    </div>

    <script>
        // Tab switching functionality
        function switchTab(tab) {
            const loginForm = document.getElementById('loginForm');
            const registerForm = document.getElementById('registerForm');
            const tabBtns = document.querySelectorAll('.tab-btn');

            // Clear messages
            hideMessages();

            if (tab === 'login') {
                loginForm.classList.add('active');
                registerForm.classList.remove('active');
                tabBtns[0].classList.add('active');
                tabBtns[1].classList.remove('active');
            } else {
                registerForm.classList.add('active');
                loginForm.classList.remove('active');
                tabBtns[1].classList.add('active');
                tabBtns[0].classList.remove('active');
            }
        }

        // Password visibility toggle
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling;

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Message functions
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('successMessage').style.display = 'none';
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // Form validation
        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        function validatePassword(password) {
            return password.length >= 6;
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (!validateEmail(email)) {
                showError('Please enter a valid email address');
                return;
            }

            if (!validatePassword(password)) {
                showError('Password must contain at least 6 characters');
                return;
            }

            // Simulate login process
            showSuccess('Login successful!');

            // Here you would typically send data to server
            console.log('Login attempt:', { email, password });
        });

        // Register form handler
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('registerName').value;
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (name.length < 2) {
                showError('Name must contain at least 2 characters');
                return;
            }

            if (!validateEmail(email)) {
                showError('Please enter a valid email address');
                return;
            }

            if (!validatePassword(password)) {
                showError('Password must contain at least 6 characters');
                return;
            }

            if (password !== confirmPassword) {
                showError('Passwords do not match');
                return;
            }

            // Simulate registration process
            showSuccess('Registration successful!');

            // Here you would typically send data to server
            console.log('Registration attempt:', { name, email, password });
        });

        // Social login handlers
        function socialLogin(provider) {
            showSuccess(`${provider} login coming soon`);
            console.log(`Social login with ${provider}`);
        }

        // Forgot password handler
        function showForgotPassword() {
            const email = prompt('Enter your email:');
            if (email && validateEmail(email)) {
                showSuccess('Password reset instructions sent to your email');
            } else if (email) {
                showError('Please enter a valid email address');
            }
        }

        // Add smooth animations on load
        window.addEventListener('load', function() {
            document.querySelector('.auth-container').style.opacity = '0';
            document.querySelector('.auth-container').style.transform = 'translateY(20px)';

            setTimeout(() => {
                document.querySelector('.auth-container').style.transition = 'all 0.6s ease';
                document.querySelector('.auth-container').style.opacity = '1';
                document.querySelector('.auth-container').style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>